require("dotenv").config();
const express = require("express");
const uploadRoutes = require("./routes/uploadRoutes");
const extractRoutes = require("./routes/extract.route");
const convertTpkRoutes = require("./routes/convertTpk.route");
const errorHandler = require("./middlewares/errorHandler");
const logger = require("./middlewares/logger");

const app = express();
const cors = require("cors");

// CORS configuration
app.use(
  cors({
    origin: ["http://localhost:5173", "http://localhost:3000"],
    methods: ["GET", "POST"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true,
  })
);

// Configure middleware - Remove body parser limits for large files
app.use(express.json({ limit: "50gb" }));
app.use(express.urlencoded({ limit: "50gb", extended: true }));
app.use(express.raw({ limit: "50gb" }));
app.use(logger);

// Routes
app.use("/api/upload", uploadRoutes);
app.use("/api/extract", extractRoutes);
app.use("/api/convert-tpk", convertTpkRoutes);
app.get("/test", (req, res) => {
  res.send(`
        <h2>API Test thành công!</h2>
        <p>Nếu bạn thấy dòng này nghĩa là backend đang chạy OK và KHÔNG bị lỗi CORS với GET /test.</p>
        <p>Origin: ${req.headers.origin || "None"}</p>
    `);
});

// Error handling
app.use(errorHandler);

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log("process.env.PORT = ", process.env.PORT);

  console.log(`Server started on port ${PORT}`);
});
