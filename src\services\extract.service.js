const fs = require("fs");
const path = require("path");
const { exec } = require("child_process");

// Helper: Đặt tên mới nếu trùng lặp trong destDir
function getUniqueDestPath(destDir, name) {
  let base = name;
  let ext = "";
  const dotIdx = name.lastIndexOf(".");
  if (dotIdx > 0 && !name.endsWith(".")) {
    base = name.substring(0, dotIdx);
    ext = name.substring(dotIdx);
  }
  let count = 1;
  let destName = name;
  while (fs.existsSync(path.join(destDir, destName))) {
    destName = `${base}_${count}${ext}`;
    count++;
  }
  return path.join(destDir, destName);
}

exports.extractAndCopy = (body) => {
  return new Promise((resolve, reject) => {
    const { archiveFile, tempExtractDir, finalDestDir, scriptPath } = body;
    if (!archiveFile || !tempExtractDir || !finalDestDir || !scriptPath) {
      return reject(
        new Error(
          "Thiếu tham số: archiveFile, tempExtractDir, finalDestDir, scriptPath"
        )
      );
    }

    const timeStart = new Date();

    exec(
      `${scriptPath} "${archiveFile}" "${tempExtractDir}"`,
      (error, stdout, stderr) => {
        if (error) {
          return reject(new Error(`Giải nén lỗi: ${error.message}`));
        }

        exec(`mkdir -p "${finalDestDir}"`, (mkErr) => {
          if (mkErr) {
            return reject(new Error(`Tạo thư mục đích lỗi: ${mkErr.message}`));
          }

          fs.readdir(tempExtractDir, (err, files) => {
            if (err) {
              return reject(
                new Error(`Không đọc được thư mục tạm: ${err.message}`)
              );
            }

            const visibleFiles = files.filter((f) => !f.startsWith("."));
            if (visibleFiles.length === 0) {
              return reject(
                new Error("Không tìm thấy file/thư mục nào vừa được giải nén.")
              );
            }

            // Copy từng file/thư mục, xử lý nếu trùng tên sẽ đổi tên
            const copiedNames = [];
            const copyPromises = visibleFiles.map((name) => {
              const src = path.join(tempExtractDir, name);
              const uniqueDest = getUniqueDestPath(finalDestDir, name);
              copiedNames.push({ src, dest: uniqueDest });
              return new Promise((rs, rj) => {
                exec(`cp -r "${src}" "${uniqueDest}"`, (copyErr) => {
                  if (copyErr) rj(copyErr);
                  else rs();
                });
              });
            });

            Promise.all(copyPromises)
              .then(() => {
                // Lấy danh sách file/folder cấp 1 sau copy
                let copiedObjects = [];
                try {
                  const items = fs
                    .readdirSync(finalDestDir)
                    .filter((f) => !f.startsWith("."));
                  copiedObjects = items.map((name) => {
                    const fullPath = path.join(finalDestDir, name);
                    const stat = fs.statSync(fullPath);
                    return {
                      name: name,
                      path: fullPath,
                      type: stat.isDirectory() ? "directory" : "file",
                      size: stat.size,
                    };
                  });
                } catch (e) {}
                const timeEnd = new Date();
                resolve({
                  finalDestDir,
                  copiedObjects,
                  fileCount: copiedObjects.length,
                  renamed: copiedNames,
                  startedAt: timeStart,
                  finishedAt: timeEnd,
                  durationSeconds: ((timeEnd - timeStart) / 1000).toFixed(2),
                  message:
                    "Giải nén và copy thành công! (đã tự động đổi tên nếu trùng lặp)",
                });
              })
              .catch((copyErr) => {
                reject(new Error(`Copy lỗi: ${copyErr.message}`));
              });
          });
        });
      }
    );
  });
};

exports.copyOnly = (body) => {
  return new Promise((resolve, reject) => {
    const { srcDir, destDir } = body;
    if (!srcDir || !destDir) {
      return reject(new Error("Thiếu tham số: srcDir, destDir"));
    }

    exec(`mkdir -p "${destDir}"`, (mkErr) => {
      if (mkErr) {
        return reject(new Error(`Tạo thư mục đích lỗi: ${mkErr.message}`));
      }

      fs.stat(srcDir, (err, stat) => {
        if (err) {
          return reject(new Error(`Không tìm thấy nguồn: ${err.message}`));
        }

        // Nếu là file
        if (stat.isFile()) {
          const baseName = path.basename(srcDir);
          const uniqueDest = getUniqueDestPath(destDir, baseName);
          exec(`cp "${srcDir}" "${uniqueDest}"`, (copyErr) => {
            if (copyErr) {
              return reject(new Error(`Copy lỗi: ${copyErr.message}`));
            }
            // Trả về đúng cấu trúc như cũ nhưng chỉ 1 file
            const stat2 = fs.statSync(uniqueDest);
            resolve({
              destDir,
              copiedObjects: [
                {
                  name: path.basename(uniqueDest),
                  path: uniqueDest,
                  type: "file",
                  size: stat2.size,
                },
              ],
              fileCount: 1,
              renamed: [{ src: srcDir, dest: uniqueDest }],
              message:
                "Copy thành công! (file đơn lẻ, đã xử lý trùng tên nếu có)",
            });
          });
        }
        // Nếu là thư mục
        else if (stat.isDirectory()) {
          fs.readdir(srcDir, (err, files) => {
            if (err) {
              return reject(
                new Error(`Không đọc được thư mục nguồn: ${err.message}`)
              );
            }
            const visibleFiles = files.filter((f) => !f.startsWith("."));
            if (visibleFiles.length === 0) {
              return reject(
                new Error(
                  "Không tìm thấy file/thư mục nào trong thư mục nguồn."
                )
              );
            }
            const copiedNames = [];
            const copyPromises = visibleFiles.map((name) => {
              const src = path.join(srcDir, name);
              const uniqueDest = getUniqueDestPath(destDir, name);
              copiedNames.push({ src, dest: uniqueDest });
              return new Promise((rs, rj) => {
                exec(`cp -r "${src}" "${uniqueDest}"`, (copyErr) => {
                  if (copyErr) rj(copyErr);
                  else rs();
                });
              });
            });

            Promise.all(copyPromises)
              .then(() => {
                // Lấy danh sách file/folder cấp 1 sau copy
                let copiedObjects = [];
                try {
                  const items = fs
                    .readdirSync(destDir)
                    .filter((f) => !f.startsWith("."));
                  copiedObjects = items.map((name) => {
                    const fullPath = path.join(destDir, name);
                    const stat = fs.statSync(fullPath);
                    return {
                      name: name,
                      path: fullPath,
                      type: stat.isDirectory() ? "directory" : "file",
                      size: stat.size,
                    };
                  });
                } catch (e) {}
                resolve({
                  destDir,
                  copiedObjects,
                  fileCount: copiedObjects.length,
                  renamed: copiedNames,
                  message:
                    "Copy thành công! (đã tự động đổi tên nếu trùng lặp)",
                });
              })
              .catch((copyErr) => {
                reject(new Error(`Copy lỗi: ${copyErr.message}`));
              });
          });
        } else {
          return reject(new Error("Nguồn không phải là file hoặc thư mục!"));
        }
      });
    });
  });
};

// Simple extract function for uploadService
exports.extract = (filePath, destDir) => {
  return new Promise((resolve, reject) => {
    const ext = path.extname(filePath).toLowerCase();

    // Check if file is an archive
    const archiveExtensions = ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'];
    if (!archiveExtensions.includes(ext)) {
      resolve(`File ${filePath} is not an archive, skipping extraction`);
      return;
    }

    // For now, just return a message that extraction is not implemented
    resolve(`Extraction not implemented for ${ext} files`);
  });
};

