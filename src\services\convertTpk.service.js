// convertTpk.service.js (CommonJS)
const { spawn } = require('child_process');
const fs = require('fs');
require('dotenv').config();

/**
 * Convert .tpk -> XYZ folder via shell script
 *
 * @param {Object} opts
 * @param {string} opts.inputTpk   - path to .tpk
 * @param {string} opts.outputDir  - output directory for tiles
 * @param {string} [opts.zoom]     - "0-15" or "0,1,2" (script sẽ parse "0-15")
 * @param {boolean} [opts.noDropEmpty=false] - keep empty tiles if true
 * @param {boolean} [opts.overwrite=false]   - allow overwrite existing output
 * @param {string}  [opts.scheme]  - "xyz" | "arcgis" (ưu tiên tham số > .env > "xyz")
 * @param {string}  [opts.venvDir] - path to python venv (ưu tiên tham số > .env)
 * @param {string}  [opts.pythonBin] - custom python path (ưu tiên tham số > .env)
 * @returns {Promise<Object>} JSON from script
 */
function convertTpkToXyz({
  inputTpk,
  outputDir,
  zoom,
  noDropEmpty = false,
  overwrite = false,
  scheme,
  venvDir,
  pythonBin,
}) {
  console.log("Thực hiện hàm này Convert Tpk to XYZ");
  

  return new Promise((resolve, reject) => {
    const scriptPath = process.env.SCRIPT_TPK_TO_XYZ_PATH || '/opt/venvs/tpk_convert_xyz.sh'; // bắt buộc cấu hình
    console.log("Đường dẫn scriptPath: ", scriptPath);
    
    if (!scriptPath || !fs.existsSync(scriptPath)) {
      return reject({
        status: 'error',
        code: 'SCRIPT_NOT_FOUND',
        error: `SCRIPT_TPK_TO_XYZ_PATH not set or file not found: ${scriptPath || '(unset)'}`,
      });
    }

    // Ưu tiên: param > .env > default
    const effectiveScheme = (scheme || process.env.TPK_SCHEME || 'xyz').toLowerCase();
    const effectiveVenv   = venvDir || process.env.VENV_DIR || process.env.TPK_VENV_DIR; // cho phép 2 key env
    const effectivePy     = pythonBin || process.env.PYTHON_BIN;

    const args = ['-i', inputTpk, '-o', outputDir];

    if (zoom) args.push('-z', String(zoom));
    // scheme: xyz | arcgis
    if (effectiveScheme === 'xyz' || effectiveScheme === 'arcgis') {
      args.push('--scheme', effectiveScheme);
    }

    if (noDropEmpty) args.push('--no-drop-empty');
    if (overwrite)   args.push('--overwrite');
    if (effectiveVenv) args.push('--venv-dir', effectiveVenv);
    if (effectivePy)   args.push('--python', effectivePy);

    const child = spawn(scriptPath, args, { stdio: ['ignore', 'pipe', 'pipe'] });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', d => (stdout += d.toString()));
    child.stderr.on('data', d => (stderr += d.toString()));

    child.on('error', (err) => {
      reject({ status: 'error', code: 'SPAWN_ERROR', error: String(err) });
    });

    child.on('close', code => {
      console.log('=== stdout ===', stdout);
      console.log('=== stderr ===', stderr);
      console.log('=== code ===', code);
      if (code === 0) {
        // Script in JSON khi ok
        try {
          resolve(JSON.parse(stdout.trim()));
        } catch {
          resolve({ status: 'ok', raw: stdout.trim() });
        }
      } else {
        // Script in JSON khi lỗi; nếu không parse được thì trả thẳng stderr
        try {
          reject(JSON.parse(stderr.trim()));
        } catch {
          reject({ status: 'error', code, error: (stderr.trim() || stdout.trim()) });
        }
      }
    });
  });
}

module.exports = { convertTpkToXyz };
