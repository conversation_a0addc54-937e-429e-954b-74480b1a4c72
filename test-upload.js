const axios = require("axios");
const fs = require("fs");
const crypto = require("crypto");
const FormData = require("form-data");

const baseURL = "http://localhost:3000";

// Helper function to create a test file
function createTestFile() {
  const content = "This is a test file for chunk upload testing. ".repeat(100);
  const buffer = Buffer.from(content);
  return buffer;
}

// Helper function to calculate hash
function calculateHash(buffer) {
  return crypto.createHash("sha256").update(buffer).digest("hex");
}

// Test function
async function testUpload() {
  try {
    console.log("Testing upload service...");

    // Test 1: Check if service is running
    console.log("\n1. Testing service availability...");
    try {
      const testResponse = await axios.get(`${baseURL}/api/upload/test`);
      console.log("✓ Service is running:", testResponse.data);
    } catch (error) {
      console.log(
        "❌ Service not running. Please start the server with: npm start"
      );
      return;
    }

    // Test 2: List active uploads
    console.log("\n2. Listing active uploads...");
    const uploadsResponse = await axios.get(
      `${baseURL}/api/upload/debug/uploads`
    );
    console.log("✓ Active uploads:", uploadsResponse.data);

    // Test 3: Initialize a test upload
    console.log("\n3. Testing upload initialization...");
    const testBuffer = createTestFile();
    const fileHash = calculateHash(testBuffer);

    const initData = {
      filename: "test-file.txt",
      filesize: testBuffer.length,
      filehash: fileHash,
      totalChunks: 1,
    };

    const initResponse = await axios.post(
      `${baseURL}/api/upload/init-upload`,
      initData
    );
    console.log("✓ Upload initialized:", initResponse.data);

    const uploadId = initResponse.data.uploadId;

    // Test 4: Check upload status
    console.log("\n4. Testing upload status...");
    const statusResponse = await axios.get(
      `${baseURL}/api/upload/upload-status`,
      {
        params: { uploadId },
      }
    );
    console.log("✓ Upload status:", statusResponse.data);

    // Test 5: Upload a chunk
    console.log("\n5. Testing chunk upload...");
    const chunkHash = calculateHash(testBuffer);

    const formData = new FormData();
    formData.append("uploadId", uploadId);
    formData.append("chunkIndex", "0");
    formData.append("chunkHash", chunkHash);
    formData.append("chunk", testBuffer, {
      filename: "chunk_0",
      contentType: "application/octet-stream",
    });

    const chunkResponse = await axios.post(
      `${baseURL}/api/upload/upload-chunk`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: 30000,
      }
    );
    console.log("✓ Chunk uploaded:", chunkResponse.data);

    // Test 6: Check status after chunk upload
    console.log("\n6. Checking status after chunk upload...");
    const statusAfterChunk = await axios.get(
      `${baseURL}/api/upload/upload-status`,
      {
        params: { uploadId },
      }
    );
    console.log("✓ Status after chunk:", statusAfterChunk.data);

    // Test 7: Complete upload
    console.log("\n7. Testing upload completion...");
    const completeResponse = await axios.post(
      `${baseURL}/api/upload/complete-upload`,
      {
        uploadId,
      }
    );
    console.log("✓ Upload completed:", completeResponse.data);

    console.log("\n✅ All tests passed!");
  } catch (error) {
    console.error("❌ Test failed:", {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
  }
}

// Run tests
testUpload();
