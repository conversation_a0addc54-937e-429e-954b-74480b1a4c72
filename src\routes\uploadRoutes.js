const express = require("express");
const multer = require("multer");
const uploadController = require("../controllers/uploadController");
const path = require("path");
const fs = require("fs-extra");
const { UPLOAD_DIR, CHUNK_SIZE } = require("../config/paths");
const router = express.Router();

// Cấu hình chi tiết cho multer
const storage = multer.diskStorage({
  destination: async function (req, file, cb) {
    try {
      // Lưu tạm vào thư mục uploads/{uploadId}
      const uploadDir = path.join(UPLOAD_DIR, req.body.uploadId || "temp");

      // Ensure directory exists before multer tries to save
      await fs.ensureDir(uploadDir);
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: function (req, file, cb) {
    // Đặt tên file là chunk_{index}
    cb(null, `chunk_${req.body.chunkIndex}`);
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: CHUNK_SIZE * 1024 * 1024, // chunk size + 1MB buffer
    files: 1, // Chỉ cho phép upload 1 file mỗi lần
  },
  fileFilter: function (req, file, cb) {
    // Kiểm tra mime type nếu cần
    cb(null, true);
  },
});

// Test endpoint
router.get("/test", (req, res) => {
  res.json({
    message: "Upload service is running",
    timestamp: new Date().toISOString(),
    chunkSize: CHUNK_SIZE,
  });
});

// Upload endpoints
router.post("/init-upload", uploadController.initUpload);
router.post(
  "/upload-chunk",
  upload.single("chunk"),
  uploadController.uploadChunk
);
router.get("/upload-status", uploadController.uploadStatus);
router.post("/complete-upload", uploadController.completeUpload);

// Debug endpoint to list active uploads
router.get("/debug/uploads", uploadController.debugUploads);

module.exports = router;
